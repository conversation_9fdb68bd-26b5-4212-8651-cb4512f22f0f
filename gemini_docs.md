🛠️ Step-by-Step Guide to Integrating Gemini API with Base64 Audio and Image Uploads

1. Obtain Your Gemini API Key
Sign up at Google AI Studio to get your free API key.
2. Install the Google Gen AI SDK
For Python, install the SDK using pip:
  pip install google-genai
3. Set Up the Client
Initialize the client with your API key:
  from google import genai

  client = genai.Client(api_key="YOUR_API_KEY")
4. Upload Media Files
For audio and image files, you can either upload them directly as base64-encoded data or use the Files API for larger files.
4.1 Base64 Encoding

Use Python's built-in base64 library to encode your files:
  import base64

  def encode_file(file_path):
      with open(file_path, "rb") as file:
          encoded_string = base64.b64encode(file.read()).decode("utf-8")
      return encoded_string5. Generate Content with Multimodal Inputs
To process both audio and image inputs, construct a prompt with the appropriate MIME types:
  from google.genai import types

  # Encode media files
  audio_base64 = encode_file("path_to_audio.mp3")
  image_base64 = encode_file("path_to_image.jpg")

  # Create Part objects
  audio_part = types.Part.from_bytes(data=audio_base64.encode(), mime_type="audio/mp3")
  image_part = types.Part.from_bytes(data=image_base64.encode(), mime_type="image/jpeg")

  # Generate content
  response = client.models.generate_content(
      model="gemini-2.0-flash",
      contents=[
          "Describe the following media:",
          audio_part,
          image_part
      ]
  )

  print(response.text)

This will send both the audio and image data to the Gemini model for processing.

