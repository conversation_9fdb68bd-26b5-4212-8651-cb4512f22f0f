'use server'

import { redirect } from 'next/navigation'
import bcrypt from 'bcryptjs'
import { createClient } from '@/lib/supabase/server'
import { createSession, deleteSession } from '@/lib/auth/session'
import { SignupFormSchema, LoginFormSchema } from '@/lib/validations'
import { FormState } from '@/lib/types'

export async function signup(state: FormState, formData: FormData): Promise<FormState> {
  // Validate form fields
  const validatedFields = SignupFormSchema.safeParse({
    name: formData.get('name'),
    email: formData.get('email'),
    password: formData.get('password'),
    clinic_name: formData.get('clinic_name'),
    phone: formData.get('phone'),
  })

  // If any form fields are invalid, return early
  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    }
  }

  // Prepare data for insertion into database
  const { name, email, password, clinic_name, phone } = validatedFields.data
  const hashedPassword = await bcrypt.hash(password, 10)

  try {
    const supabase = await createClient()

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('doctors')
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      return {
        message: 'An account with this email already exists.',
      }
    }

    // Insert the user into the database
    const { data: user, error } = await supabase
      .from('doctors')
      .insert({
        name,
        email,
        password_hash: hashedPassword,
        clinic_name,
        phone,
      })
      .select('id')
      .single()

    if (error) {
      console.error('Database error:', error)
      return {
        message: 'An error occurred while creating your account.',
      }
    }

    if (!user) {
      return {
        message: 'An error occurred while creating your account.',
      }
    }

    // Create user session
    await createSession(user.id)
  } catch (error) {
    console.error('Signup error:', error)
    return {
      message: 'An unexpected error occurred.',
    }
  }

  // Redirect user to dashboard
  redirect('/dashboard')
}

export async function login(state: FormState, formData: FormData): Promise<FormState> {
  console.log('Login attempt started')
  console.log('Form data keys:', Array.from(formData.keys()))

  // Validate form fields
  const validatedFields = LoginFormSchema.safeParse({
    email: formData.get('email'),
    password: formData.get('password'),
  })

  console.log('Validation result:', validatedFields.success)

  // If any form fields are invalid, return early
  if (!validatedFields.success) {
    console.log('Validation errors:', validatedFields.error.flatten().fieldErrors)
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    }
  }

  const { email, password } = validatedFields.data
  console.log('Login data:', { email, passwordLength: password.length })

  try {
    const supabase = await createClient()
    console.log('Supabase client created')

    // Get user from database
    const { data: user, error } = await supabase
      .from('doctors')
      .select('id, password_hash')
      .eq('email', email)
      .single()

    console.log('Database query result:', { user: user ? 'found' : 'not found', error: error?.message })

    if (error || !user) {
      console.log('User not found or database error')
      return {
        message: 'Invalid email or password.',
      }
    }

    // Verify password
    console.log('Verifying password...')
    const isValidPassword = await bcrypt.compare(password, user.password_hash)
    console.log('Password valid:', isValidPassword)

    if (!isValidPassword) {
      console.log('Invalid password')
      return {
        message: 'Invalid email or password.',
      }
    }

    // Create user session
    console.log('Creating session for user:', user.id)
    await createSession(user.id)
    console.log('Session created successfully')
  } catch (error) {
    console.error('Login error:', error)
    return {
      message: 'An unexpected error occurred.',
    }
  }

  // Redirect user to dashboard
  redirect('/dashboard')
}

export async function logout() {
  await deleteSession()
  redirect('/login')
}
