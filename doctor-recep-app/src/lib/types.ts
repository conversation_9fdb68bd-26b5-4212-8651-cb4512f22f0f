// TypeScript types for the Doctor Voice & Image Summary System

export interface Doctor {
  id: string;
  email: string;
  name: string;
  phone?: string;
  clinic_name?: string;
  template_config: TemplateConfig;
  created_at: string;
  updated_at: string;
}

export interface TemplateConfig {
  prescription_format: 'standard' | 'detailed' | 'minimal';
  language: 'english' | 'hindi' | 'tamil' | 'telugu' | 'bengali';
  tone: 'professional' | 'friendly' | 'formal';
  sections: string[];
}

export interface Consultation {
  id: string;
  doctor_id: string;
  submitted_by: 'doctor' | 'receptionist';
  audio_base64: string;
  image_base64?: string;
  ai_generated_note?: string;
  edited_note?: string;
  status: 'pending' | 'generated' | 'approved';
  patient_number?: number;
  created_at: string;
  updated_at: string;
}

export interface SessionPayload {
  userId: string;
  expiresAt: Date;
}

export interface FormState {
  errors?: {
    name?: string[];
    email?: string[];
    password?: string[];
    clinic_name?: string[];
  };
  message?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ConsultationCreateRequest {
  audio_base64: string;
  image_base64?: string;
  submitted_by: 'doctor' | 'receptionist';
}

export interface ConsultationGenerateRequest {
  consultation_id: string;
}

export interface ConsultationApproveRequest {
  consultation_id: string;
  edited_note: string;
}

export interface GeminiApiRequest {
  audio_base64: string;
  image_base64?: string;
  template_config: TemplateConfig;
  submitted_by: 'doctor' | 'receptionist';
}

export interface GeminiApiResponse {
  generated_summary: string;
  confidence_score?: number;
}

// Audio recording types
export interface AudioRecordingState {
  isRecording: boolean;
  audioBlob?: Blob;
  audioBase64?: string;
  duration: number;
  error?: string;
}

// Image capture types
export interface ImageCaptureState {
  imageBlob?: Blob;
  imageBase64?: string;
  preview?: string;
  error?: string;
}

// Dashboard types
export interface DashboardStats {
  total_consultations: number;
  pending_consultations: number;
  generated_consultations: number;
  approved_consultations: number;
  today_consultations: number;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
}

// WhatsApp integration types (for future use)
export interface WhatsAppMessage {
  to: string;
  message: string;
  consultation_id: string;
}
