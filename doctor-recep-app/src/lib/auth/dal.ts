import 'server-only'
import { cache } from 'react'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { decrypt } from './session'
import { createClient } from '@/lib/supabase/server'
import { <PERSON> } from '@/lib/types'

export const verifySession = cache(async () => {
  const cookieStore = await cookies()
  const cookie = cookieStore.get('session')?.value
  const session = await decrypt(cookie)

  if (!session?.userId) {
    redirect('/login')
  }

  return { isAuth: true, userId: session.userId }
})

export const getUser = cache(async (): Promise<Doctor | null> => {
  const session = await verifySession()
  if (!session) return null

  try {
    const supabase = await createClient()
    const { data: user, error } = await supabase
      .from('doctors')
      .select('id, email, name, phone, clinic_name, template_config, created_at, updated_at')
      .eq('id', session.userId)
      .single()

    if (error) {
      console.error('Failed to fetch user:', error)
      return null
    }

    return user as Doctor
  } catch (error) {
    console.error('Failed to fetch user:', error)
    return null
  }
})

export const getUserById = cache(async (userId: string): Promise<Doctor | null> => {
  try {
    const supabase = await createClient()
    const { data: user, error } = await supabase
      .from('doctors')
      .select('id, email, name, phone, clinic_name, template_config, created_at, updated_at')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Failed to fetch user by ID:', error)
      return null
    }

    return user as Doctor
  } catch (error) {
    console.error('Failed to fetch user by ID:', error)
    return null
  }
})
