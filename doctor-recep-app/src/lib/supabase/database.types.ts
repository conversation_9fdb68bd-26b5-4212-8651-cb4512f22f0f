// Auto-generated types for Supabase database schema
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      doctors: {
        Row: {
          id: string
          email: string
          password_hash: string
          name: string
          phone: string | null
          clinic_name: string | null
          template_config: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          name: string
          phone?: string | null
          clinic_name?: string | null
          template_config?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          name?: string
          phone?: string | null
          clinic_name?: string | null
          template_config?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      consultations: {
        Row: {
          id: string
          doctor_id: string
          submitted_by: 'doctor' | 'receptionist'
          audio_base64: string
          image_base64: string | null
          ai_generated_note: string | null
          edited_note: string | null
          status: 'pending' | 'generated' | 'approved'
          patient_number: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          submitted_by: 'doctor' | 'receptionist'
          audio_base64: string
          image_base64?: string | null
          ai_generated_note?: string | null
          edited_note?: string | null
          status?: 'pending' | 'generated' | 'approved'
          patient_number?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          submitted_by?: 'doctor' | 'receptionist'
          audio_base64?: string
          image_base64?: string | null
          ai_generated_note?: string | null
          edited_note?: string | null
          status?: 'pending' | 'generated' | 'approved'
          patient_number?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "consultations_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
