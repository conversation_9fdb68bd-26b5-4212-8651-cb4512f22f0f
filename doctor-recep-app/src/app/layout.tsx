import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Doctor Reception System",
  description: "AI-powered patient consultation summary system for Indian clinics",
  keywords: "doctor, patient, consultation, AI, summary, clinic, healthcare",
  authors: [{ name: "Doctor Reception System" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  );
}
