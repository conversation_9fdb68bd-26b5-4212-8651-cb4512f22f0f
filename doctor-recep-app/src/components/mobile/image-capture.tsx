'use client'

import { useRef } from 'react'
import { Camera, Upload, X } from 'lucide-react'
import { ImageCaptureState } from '@/lib/types'
import { fileToBase64, supportsCamera } from '@/lib/utils'

interface ImageCaptureProps {
  imageState: ImageCaptureState
  onStateChange: (newState: Partial<ImageCaptureState>) => void
}

export function ImageCapture({ imageState, onStateChange }: ImageCaptureProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    try {
      onStateChange({ error: undefined })

      // Validate file type
      if (!file.type.startsWith('image/')) {
        onStateChange({ error: 'Please select a valid image file' })
        return
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        onStateChange({ error: 'Image file is too large. Please select a file under 10MB.' })
        return
      }

      const imageBase64 = await fileToBase64(file)
      const preview = URL.createObjectURL(file)

      onStateChange({
        imageBlob: file,
        imageBase64,
        preview,
      })
    } catch (error) {
      console.error('Error processing image:', error)
      onStateChange({ error: 'Failed to process image. Please try again.' })
    }
  }

  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const clearImage = () => {
    if (imageState.preview) {
      URL.revokeObjectURL(imageState.preview)
    }
    onStateChange({
      imageBlob: undefined,
      imageBase64: undefined,
      preview: undefined,
      error: undefined,
    })
    
    // Reset file inputs
    if (fileInputRef.current) fileInputRef.current.value = ''
    if (cameraInputRef.current) cameraInputRef.current.value = ''
  }

  const openCamera = () => {
    cameraInputRef.current?.click()
  }

  const openFileSelector = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      {!imageState.preview ? (
        // Capture/Upload interface
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            {/* Camera Capture */}
            {supportsCamera() && (
              <button
                onClick={openCamera}
                className="flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors"
              >
                <Camera className="w-6 h-6 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">
                  Take Photo
                </span>
              </button>
            )}

            {/* File Upload */}
            <button
              onClick={openFileSelector}
              className="flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors"
            >
              <Upload className="w-6 h-6 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">
                Upload from Gallery
              </span>
            </button>
          </div>

          {/* Hidden file inputs */}
          <input
            ref={cameraInputRef}
            type="file"
            accept="image/*"
            capture="environment"
            onChange={handleCameraCapture}
            className="hidden"
          />
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />

          {/* Instructions */}
          <div className="bg-gray-50 rounded-md p-3">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Photo Tips:</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Ensure good lighting for clear text</li>
              <li>• Keep the camera steady</li>
              <li>• Capture the entire note/prescription</li>
              <li>• Avoid shadows and glare</li>
            </ul>
          </div>
        </div>
      ) : (
        // Preview interface
        <div className="space-y-4">
          <div className="relative">
            <img
              src={imageState.preview}
              alt="Captured handwritten notes"
              className="w-full h-auto max-h-64 object-contain rounded-lg border"
            />
            <button
              onClick={clearImage}
              className="absolute top-2 right-2 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={openCamera}
              className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm"
            >
              <Camera className="w-4 h-4" />
              <span>Retake</span>
            </button>
            <button
              onClick={openFileSelector}
              className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-gray-500 hover:bg-gray-600 text-white rounded-md text-sm"
            >
              <Upload className="w-4 h-4" />
              <span>Choose Different</span>
            </button>
          </div>
        </div>
      )}

      {/* Error Display */}
      {imageState.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-700">{imageState.error}</p>
        </div>
      )}
    </div>
  )
}
