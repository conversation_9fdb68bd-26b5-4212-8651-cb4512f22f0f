'use client'

import { useState } from 'react'
import { X, Play, Pause, Wand2, Save, Download, Copy } from 'lucide-react'
import { Consultation } from '@/lib/types'
import { formatDate, formatDuration } from '@/lib/utils'
import { generateSummary, approveConsultation } from '@/lib/actions/consultations'

interface ConsultationModalProps {
  consultation: Consultation
  onClose: () => void
}

export function ConsultationModal({ consultation, onClose }: ConsultationModalProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isApproving, setIsApproving] = useState(false)
  const [editedNote, setEditedNote] = useState(consultation.edited_note || consultation.ai_generated_note || '')
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')

  const handleGenerateSummary = async () => {
    setIsGenerating(true)
    setError('')
    setSuccess('')

    try {
      const result = await generateSummary(consultation.id)
      
      if (result.success) {
        setEditedNote(result.data || '')
        setSuccess('Summary generated successfully!')
        setTimeout(() => setSuccess(''), 3000)
      } else {
        setError(result.error || 'Failed to generate summary')
      }
    } catch (error) {
      setError('An unexpected error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleApprove = async () => {
    if (!editedNote.trim()) {
      setError('Please provide a summary before approving')
      return
    }

    setIsApproving(true)
    setError('')
    setSuccess('')

    try {
      const result = await approveConsultation(consultation.id, editedNote)
      
      if (result.success) {
        setSuccess('Consultation approved successfully!')
        setTimeout(() => {
          setSuccess('')
          onClose()
        }, 2000)
      } else {
        setError(result.error || 'Failed to approve consultation')
      }
    } catch (error) {
      setError('An unexpected error occurred')
    } finally {
      setIsApproving(false)
    }
  }

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editedNote)
      setSuccess('Copied to clipboard!')
      setTimeout(() => setSuccess(''), 2000)
    } catch (error) {
      setError('Failed to copy to clipboard')
    }
  }

  const playAudio = () => {
    // Create audio element and play
    const audio = new Audio(`data:audio/webm;base64,${consultation.audio_base64}`)
    audio.play()
    setIsPlaying(true)
    
    audio.onended = () => setIsPlaying(false)
    audio.onerror = () => {
      setIsPlaying(false)
      setError('Failed to play audio')
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Patient #{consultation.patient_number || 'N/A'} - Consultation Details
            </h3>
            <p className="text-sm text-gray-600">
              Submitted {formatDate(consultation.created_at)} by {consultation.submitted_by}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="mt-6 space-y-6">
          {/* Audio Section */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Audio Recording</h4>
            <div className="flex items-center space-x-4">
              <button
                onClick={playAudio}
                disabled={isPlaying}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-md text-sm"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{isPlaying ? 'Playing...' : 'Play Audio'}</span>
              </button>
              <span className="text-sm text-gray-600">
                Consultation recording
              </span>
            </div>
          </div>

          {/* Image Section */}
          {consultation.image_base64 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Handwritten Notes</h4>
              <img
                src={`data:image/jpeg;base64,${consultation.image_base64}`}
                alt="Handwritten notes"
                className="max-w-full h-auto max-h-64 rounded border"
              />
            </div>
          )}

          {/* Summary Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">Patient Summary</h4>
              <div className="flex items-center space-x-2">
                {consultation.status === 'pending' && (
                  <button
                    onClick={handleGenerateSummary}
                    disabled={isGenerating}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded text-sm"
                  >
                    <Wand2 className="w-4 h-4" />
                    <span>{isGenerating ? 'Generating...' : 'Generate Summary'}</span>
                  </button>
                )}
                
                {editedNote && (
                  <button
                    onClick={handleCopyToClipboard}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Copy</span>
                  </button>
                )}
              </div>
            </div>

            <textarea
              value={editedNote}
              onChange={(e) => setEditedNote(e.target.value)}
              placeholder={
                consultation.status === 'pending'
                  ? 'Click "Generate Summary" to create an AI-powered patient summary...'
                  : 'Edit the patient summary as needed...'
              }
              className="w-full h-64 p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
              disabled={isGenerating}
            />
          </div>

          {/* Status Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              Status: <span className="font-medium capitalize">{consultation.status}</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
              
              {consultation.status !== 'approved' && editedNote.trim() && (
                <button
                  onClick={handleApprove}
                  disabled={isApproving || !editedNote.trim()}
                  className="flex items-center space-x-2 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:bg-green-300"
                >
                  <Save className="w-4 h-4" />
                  <span>{isApproving ? 'Approving...' : 'Approve & Save'}</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
