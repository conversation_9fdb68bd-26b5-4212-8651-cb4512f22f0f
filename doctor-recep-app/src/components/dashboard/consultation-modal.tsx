'use client'

import { useState, useRef } from 'react'
import { X, Play, Pause, Wand2, Save, Download, Copy, Camera, Upload } from 'lucide-react'
import { Consultation } from '@/lib/types'
import { formatDate, formatDuration } from '@/lib/utils'
import { generateSummary, approveConsultation } from '@/lib/actions/consultations'

interface ConsultationModalProps {
  consultation: Consultation
  onClose: () => void
}

export function ConsultationModal({ consultation, onClose }: ConsultationModalProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isApproving, setIsApproving] = useState(false)
  const [editedNote, setEditedNote] = useState(consultation.edited_note || consultation.ai_generated_note || '')
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')

  // Additional images state for nurses
  const [additionalImages, setAdditionalImages] = useState<Array<{ id: string, file: File, preview: string, base64: string }>>([])
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const cameraInputRef = useRef<HTMLInputElement | null>(null)

  // Convert blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result.split(',')[1]) // Remove data:image/jpeg;base64, prefix
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  // Handle additional image upload
  const handleImageUpload = async (files: FileList) => {
    try {
      const newImages = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        if (!file.type.startsWith('image/')) continue
        if (file.size > 10 * 1024 * 1024) continue // 10MB limit

        const preview = URL.createObjectURL(file)
        const base64 = await blobToBase64(file)

        newImages.push({
          id: `${Date.now()}-${i}`,
          file,
          preview,
          base64
        })
      }

      setAdditionalImages(prev => [...prev, ...newImages])
    } catch (err) {
      setError('Failed to process images')
    }
  }

  // Remove additional image
  const removeAdditionalImage = (id: string) => {
    setAdditionalImages(prev => {
      const imageToRemove = prev.find(img => img.id === id)
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview)
      }
      return prev.filter(img => img.id !== id)
    })
  }

  const handleGenerateSummary = async () => {
    setIsGenerating(true)
    setError('')
    setSuccess('')

    try {
      // Combine original images with additional images
      const allImages = [
        ...(consultation.images_base64 || []),
        ...additionalImages.map(img => img.base64)
      ]

      const result = await generateSummary(consultation.id, allImages)

      if (result.success) {
        setEditedNote(result.data || '')
        setSuccess('Summary generated successfully!')
        setTimeout(() => setSuccess(''), 3000)
      } else {
        setError(result.error || 'Failed to generate summary')
      }
    } catch (error) {
      setError('An unexpected error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleApprove = async () => {
    if (!editedNote.trim()) {
      setError('Please provide a summary before approving')
      return
    }

    setIsApproving(true)
    setError('')
    setSuccess('')

    try {
      const result = await approveConsultation(consultation.id, editedNote)

      if (result.success) {
        setSuccess('Consultation approved successfully!')
        setTimeout(() => {
          setSuccess('')
          onClose()
        }, 2000)
      } else {
        setError(result.error || 'Failed to approve consultation')
      }
    } catch (error) {
      setError('An unexpected error occurred')
    } finally {
      setIsApproving(false)
    }
  }

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editedNote)
      setSuccess('Copied to clipboard!')
      setTimeout(() => setSuccess(''), 2000)
    } catch (error) {
      setError('Failed to copy to clipboard')
    }
  }

  const playAudio = () => {
    // Create audio element and play
    const audio = new Audio(`data:audio/webm;base64,${consultation.audio_base64}`)
    audio.play()
    setIsPlaying(true)

    audio.onended = () => setIsPlaying(false)
    audio.onerror = () => {
      setIsPlaying(false)
      setError('Failed to play audio')
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Patient #{consultation.patient_number || 'N/A'} - Consultation Details
            </h3>
            <p className="text-sm text-gray-600">
              Submitted {formatDate(consultation.created_at)} by {consultation.submitted_by}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="mt-6 space-y-6">
          {/* Audio Section */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Audio Recording</h4>
            <div className="flex items-center space-x-4">
              <button
                onClick={playAudio}
                disabled={isPlaying}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-md text-sm"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{isPlaying ? 'Playing...' : 'Play Audio'}</span>
              </button>
              <span className="text-sm text-gray-600">
                Consultation recording
              </span>
            </div>
          </div>

          {/* Images Section */}
          {consultation.images_base64 && Array.isArray(consultation.images_base64) && consultation.images_base64.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Original Images ({consultation.images_base64.length} image{consultation.images_base64.length > 1 ? 's' : ''})
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {consultation.images_base64.map((imageBase64, index) => (
                  <img
                    key={index}
                    src={`data:image/jpeg;base64,${imageBase64}`}
                    alt={`Original image ${index + 1}`}
                    className="w-full h-auto max-h-64 object-contain rounded border"
                  />
                ))}
              </div>
            </div>
          )}

          {/* Additional Images Section for Nurses */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              Additional Images (Optional)
            </h4>
            <p className="text-xs text-gray-600 mb-4">
              Nurses can add additional images before generating the summary
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
              {/* Camera button */}
              <button
                onClick={() => cameraInputRef.current?.click()}
                className="aspect-square border-2 border-dashed border-blue-300 rounded-lg flex flex-col items-center justify-center hover:border-blue-400 hover:bg-blue-100 transition-colors"
              >
                <Camera className="w-6 h-6 text-blue-400 mb-1" />
                <span className="text-xs text-blue-600">Camera</span>
              </button>

              {/* Upload button */}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="aspect-square border-2 border-dashed border-blue-300 rounded-lg flex flex-col items-center justify-center hover:border-blue-400 hover:bg-blue-100 transition-colors"
              >
                <Upload className="w-6 h-6 text-blue-400 mb-1" />
                <span className="text-xs text-blue-600">Upload</span>
              </button>

              {/* Additional image previews */}
              {additionalImages.map((image) => (
                <div key={image.id} className="relative aspect-square">
                  <img
                    src={image.preview}
                    alt="Additional image"
                    className="w-full h-full object-cover rounded-lg border"
                  />
                  <button
                    onClick={() => removeAdditionalImage(image.id)}
                    className="absolute top-1 right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>

            {additionalImages.length > 0 && (
              <p className="text-xs text-green-600">
                {additionalImages.length} additional image{additionalImages.length > 1 ? 's' : ''} will be included in the summary generation
              </p>
            )}

            {/* Hidden inputs */}
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
              className="hidden"
            />
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
              className="hidden"
            />
          </div>

          {/* Summary Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">Patient Summary</h4>
              <div className="flex items-center space-x-2">
                {consultation.status === 'pending' && (
                  <button
                    onClick={handleGenerateSummary}
                    disabled={isGenerating}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded text-sm"
                  >
                    <Wand2 className="w-4 h-4" />
                    <span>{isGenerating ? 'Generating...' : 'Generate Summary'}</span>
                  </button>
                )}

                {editedNote && (
                  <button
                    onClick={handleCopyToClipboard}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Copy</span>
                  </button>
                )}
              </div>
            </div>

            <textarea
              value={editedNote}
              onChange={(e) => setEditedNote(e.target.value)}
              placeholder={
                consultation.status === 'pending'
                  ? 'Click "Generate Summary" to create an AI-powered patient summary...'
                  : 'Edit the patient summary as needed...'
              }
              className="w-full h-64 p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
              disabled={isGenerating}
            />
          </div>

          {/* Status Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              Status: <span className="font-medium capitalize">{consultation.status}</span>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>

              {consultation.status !== 'approved' && editedNote.trim() && (
                <button
                  onClick={handleApprove}
                  disabled={isApproving || !editedNote.trim()}
                  className="flex items-center space-x-2 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:bg-green-300"
                >
                  <Save className="w-4 h-4" />
                  <span>{isApproving ? 'Approving...' : 'Approve & Save'}</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
