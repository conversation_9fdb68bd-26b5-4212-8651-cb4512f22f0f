# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Session Secret for JWT
SESSION_SECRET=your_session_secret_key_here

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key

# Cloud Run Backend URL (will be set after deployment)
NEXT_PUBLIC_API_URL=http://localhost:3001

# Environment
NODE_ENV=development
