import * as core from "zod/v4/core";
import * as schemas from "./schemas.js";
export function string(params) {
    return core._coercedString(schemas.ZodMiniString, params);
}
export function number(params) {
    return core._coercedNumber(schemas.ZodMiniNumber, params);
}
export function boolean(params) {
    return core._coercedBoolean(schemas.ZodMiniBoolean, params);
}
export function bigint(params) {
    return core._coercedBigint(schemas.ZodMiniBigInt, params);
}
export function date(params) {
    return core._coercedDate(schemas.ZodMiniDate, params);
}
