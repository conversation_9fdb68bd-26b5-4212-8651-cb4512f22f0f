-- Doctor Voice & Image-Based Patient Summary System Database Schema
-- Multi-tenant architecture with Row Level Security

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Doctors table
CREATE TABLE doctors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  clinic_name TEXT,
  template_config JSONB NOT NULL DEFAULT '{
    "prescription_format": "standard",
    "language": "english",
    "tone": "professional",
    "sections": ["symptoms", "diagnosis", "prescription", "advice", "follow_up"]
  }'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Consultations table (one per patient visit)
CREATE TABLE consultations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  submitted_by TEXT CHECK (submitted_by IN ('doctor','receptionist')) NOT NULL,
  audio_base64 TEXT NOT NULL,
  image_base64 TEXT NULL,
  ai_generated_note TEXT,
  edited_note TEXT,
  status TEXT CHECK (status IN ('pending','generated','approved')) NOT NULL DEFAULT 'pending',
  patient_number INTEGER,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_consultations_doctor_id_status ON consultations(doctor_id, status);
CREATE INDEX idx_consultations_created_at ON consultations(created_at);
CREATE INDEX idx_doctors_email ON doctors(email);

-- Enable Row Level Security
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for doctors table
CREATE POLICY "Doctors can view their own profile"
ON doctors FOR SELECT
TO authenticated
USING ((SELECT auth.uid()) = id);

CREATE POLICY "Doctors can update their own profile"
ON doctors FOR UPDATE
TO authenticated
USING ((SELECT auth.uid()) = id)
WITH CHECK ((SELECT auth.uid()) = id);

-- RLS Policies for consultations table
CREATE POLICY "Doctors can access their own consultations"
ON consultations FOR ALL
TO authenticated
USING (doctor_id = (SELECT auth.uid()));

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_doctors_updated_at BEFORE UPDATE ON doctors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_consultations_updated_at BEFORE UPDATE ON consultations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-increment patient number per doctor
CREATE OR REPLACE FUNCTION set_patient_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.patient_number IS NULL THEN
        SELECT COALESCE(MAX(patient_number), 0) + 1
        INTO NEW.patient_number
        FROM consultations
        WHERE doctor_id = NEW.doctor_id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_consultation_patient_number BEFORE INSERT ON consultations
    FOR EACH ROW EXECUTE FUNCTION set_patient_number();
